/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Utility
# Cards
# Common
# Form
# Navigations
# Animations
# Mobile Nav
# Search Popup
# Page Header
# Google Map
# Client Carousel
--------------------------------------------------------------*/

@import url("flaticon.css");
@import url("animate.min.css");
@import url("custom-animate.css");
@import url("all.min.css");
@import url("bootstrap.min.css");
@import url("module-css/about.css");
@import url("module-css/blog.css");
@import url("module-css/brand.css");
@import url("module-css/coming-soon.css");
@import url("module-css/contact.css");
@import url("module-css/counter.css");
@import url("module-css/error-page.css");
@import url("module-css/faq.css");
@import url("module-css/footer.css");
@import url("module-css/page-header.css");
@import url("module-css/services.css");
@import url("module-css/slider.css");
@import url("module-css/team.css");
@import url("module-css/testimonial.css");
@import url("module-css/video.css");
@import url("module-css/feature.css");
@import url("module-css/sliding-text.css");
@import url("module-css/projects.css");
@import url("module-css/design-interior.css");
@import url("module-css/awards.css");
@import url("module-css/how-it-work.css");
@import url("module-css/before-after.css");
@import url("module-css/united-kingdom.css");
@import url("module-css/google-map.css");

/*--------------------------------------------------------------
# Common
--------------------------------------------------------------*/
:root {
  --tecture-gray: #829995;
  --tecture-gray-rgb: 130, 153, 149;
  --tecture-white: #f7f6f3;
  --tecture-white-rgb: 247, 246, 243;
  --tecture-base: #d1ae8b;
  --tecture-base-rgb: 217, 180, 143;
  --tecture-black: #072b31;
  --tecture-black-rgb: 7, 43, 49;
  --tecture-bdr-color: rgba(255, 255, 255, 0.2);
  --tecture-bdr-color-rgb: 55, 58, 60;
}

.row {
  --bs-gutter-x: 30px;
}

.gutter-y-30 {
  --bs-gutter-y: 30px;
}

body {
  font-family: var(--tecture-font);
  font-size: 17px;
  line-height: 26px;
  font-weight: 400;
  color: var(--tecture-gray);
}

body.locked {
  overflow: hidden;
}

.body-bg-color-1 {
  position: relative;
  background-color: #0a0c09;
}

.theme-border-left {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 100px;
  width: 1px;
  background-color: var(--tecture-bdr-color);
  z-index: -1;
}

.theme-border-right {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 100px;
  width: 1px;
  background-color: var(--tecture-bdr-color);
  z-index: -1;
}

a {
  color: var(--tecture-base);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

a,
a:hover,
a:focus,
a:visited {
  text-decoration: none;
}

::-webkit-input-placeholder {
  color: inherit;
  opacity: 1;
}

:-ms-input-placeholder {
  color: inherit;
  opacity: 1;
}

::-ms-input-placeholder {
  color: inherit;
  opacity: 1;
}

::placeholder {
  color: inherit;
  opacity: 1;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--tecture-font-2);
  color: var(--tecture-white);
  margin: 0;
}

p {
  color: #aeb0b4;
  font-size: 17px;
  line-height: 26px;
  margin: 0;
}

dl,
ol,
ul {
  margin-top: 0;
  margin-bottom: 0;
}

ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

ol,
li {
  margin: 0;
  padding: 0;
}

::-webkit-input-placeholder {
  color: inherit;
  opacity: 1;
}

:-ms-input-placeholder {
  color: inherit;
  opacity: 1;
}

::-ms-input-placeholder {
  color: inherit;
  opacity: 1;
}

::placeholder {
  color: inherit;
  opacity: 1;
}

.page-wrapper {
  position: relative;
  margin: 0 auto;
  width: 100%;
  min-width: 300px;
  overflow: hidden;
}

.container {
  padding-left: 15px;
  padding-right: 15px;
}

.list-unstyled {
  padding-left: 0;
}

@media (min-width: 1400px) {
  .container {
    max-width: 1400px;
  }
}

@media (min-width: 1200px) {
  .auto-container {
    position: static;
    max-width: 1320px;
    padding: 0px 15px;
    margin: 0 auto;
  }
}

a.video-popup {
  cursor: pointer;
}

::-webkit-input-placeholder {
  color: inherit;
  opacity: 1;
}

:-ms-input-placeholder {
  color: inherit;
  opacity: 1;
}

::-ms-input-placeholder {
  color: inherit;
  opacity: 1;
}

::placeholder {
  color: inherit;
  opacity: 1;
}

.section-separator {
  border-color: var(--tecture-border);
  border-width: 1px;
  margin-top: 0;
  margin-bottom: 0;
}

#particles-js {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-position: 50% 50%;
  opacity: 0.4;
  z-index: -1;
}

.section-shape-1 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  z-index: -1;
}

.section-title {
  position: relative;
  display: block;
  margin-bottom: 58px;
  margin-top: -12px;
}

.section-title__title {
  color: var(--tecture-white);
  font-size: 54px;
  line-height: 64px;
  font-weight: 700;
  text-transform: uppercase;
}

.thm-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  -webkit-appearance: none;
  appearance: none;
  outline: none !important;
  font-weight: 700;
  font-size: 17px;
  color: var(--tecture-white);
  background-color: var(--tecture-base);
  padding: 17px 40px 17px;
  border-radius: 0;
  moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  overflow: hidden;
  font-family: var(--tecture-font);
  text-transform: capitalize;
  gap: 5px;
  z-index: 1;
}

.thm-btn span {
  font-size: 23px;
  position: relative;
  display: inline-block;
  top: -1px;
}

.thm-btn:before {
  content: "";
  background-color: var(--tecture-white);
  width: 50%;
  height: 0;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: -1;
}

.thm-btn:after {
  content: "";
  background-color: var(--tecture-white);
  width: 50%;
  height: 0;
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: -1;
}

.thm-btn:hover:before,
.thm-btn:hover:after {
  height: 100%;
}

.thm-btn:hover {
  color: var(--tecture-black);
}

.dwn-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  -webkit-appearance: none;
  appearance: none;
  outline: none !important;
  font-weight: 700;
  font-size: 17px;
  color: var(--tecture-black);
  background-color: var(--tecture-white);
  padding: 17px 40px 17px;
  border-radius: 0;
  moz-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  overflow: hidden;
  font-family: var(--tecture-font);
  text-transform: capitalize;
  gap: 5px;
  z-index: 1;
}

.dwn-btn span {
  font-size: 23px;
  position: relative;
  display: inline-block;
  top: -1px;
}

.dwn-btn:before {
  content: "";
  background-color: var(--tecture-black);
  width: 50%;
  height: 0;
  position: absolute;
  top: 0;
  left: 0;
  transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: -1;
}

.dwn-btn:after {
  content: "";
  background-color: var(--tecture-black);
  width: 50%;
  height: 0;
  position: absolute;
  bottom: 0;
  right: 0;
  transition: all 0.3s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  z-index: -1;
}

.dwn-btn:hover:before,
.dwn-btn:hover:after {
  height: 100%;
}

.dwn-btn:hover {
  color: var(--tecture-base);
}

.preloader {
  position: fixed;
  background-color: #fff;
  background-position: center center;
  background-repeat: no-repeat;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
}

.preloader__image {
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  background-image: url(../images/loader.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 60px auto;
  width: 100%;
  height: 100%;
}

/* scroll to top */
.scroll-to-top {
  display: flex;
  align-items: center;
  width: auto;
  height: 35px;
  background: transparent;
  position: fixed;
  bottom: 60px;
  right: -12px;
  z-index: 99;
  text-align: center;
  transform: rotate(-90deg);
  cursor: pointer;
  transition: all 0.2s ease;
}

.scroll-to-top:hover {
  color: var(--tecture-base);
}

.scroll-to-top__text {
  display: inline;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 700;
  margin-left: 10px;
}

.scroll-to-top__wrapper {
  display: inline-block;
  width: 30px;
  height: 4px;
  background-color: var(--tecture-base);
  position: relative;
  overflow: hidden;
}

.scroll-to-top__inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--tecture-bdr-color);
}

.scroll-to-top.show {
  opacity: 1;
  visibility: visible;
  bottom: 70px;
}

/** xs sidebar **/
.xs-sidebar-group .xs-overlay {
  left: 0%;
  top: 0;
  position: fixed;
  height: 100%;
  opacity: 0;
  width: 100%;
  visibility: hidden;
  -webkit-transition: all 0.4s ease-in 0.8s;
  -o-transition: all 0.4s ease-in 0.8s;
  transition: all 0.4s ease-in 0.8s;
  cursor: url(../images/icon/cross-out.png), pointer;
  z-index: 999;
}

.xs-sidebar-group.isActive .xs-overlay {
  opacity: 0.8;
  visibility: visible;
  -webkit-transition: all 0.8s ease-out 0s;
  -o-transition: all 0.8s ease-out 0s;
  transition: all 0.8s ease-out 0s;
  right: 100%;
}

.xs-sidebar-group .widget-heading {
  position: absolute;
  top: 0;
  right: 0;
  padding: 25px;
}

.xs-sidebar-group .widget-heading a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border: 2px solid #fff;
  border-radius: 50%;
  color: #fff;
  font-size: 16px;
  font-weight: 400;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.xs-sidebar-group .widget-heading a:hover {
  color: var(--tecture-base);
  border-color: var(--tecture-base);
}

.xs-sidebar-widget {
  position: fixed;
  left: -100%;
  top: 0;
  bottom: 0;
  width: 100%;
  max-width: 360px;
  z-index: 999999;
  overflow: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  -webkit-transition: all 0.5s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
  -o-transition: all 0.5s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
  transition: all 0.5s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
  visibility: hidden;
  opacity: 0;
}

.xs-sidebar-group.isActive .xs-sidebar-widget {
  opacity: 1;
  visibility: visible;
  left: 0;
  background-color: var(--tecture-black);
  -webkit-transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.4s;
  -o-transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.4s;
  transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.4s;
}

.sidebar-textwidget {
  padding: 70px 30px;
}

.sidebar-widget-container {
  position: relative;
  top: 150px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-in 0.3s;
  -o-transition: all 0.3s ease-in 0.3s;
  transition: all 0.3s ease-in 0.3s;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.xs-sidebar-group.isActive .sidebar-widget-container {
  top: 0px;
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 1s ease-out 1.2s;
  -o-transition: all 1s ease-out 1.2s;
  transition: all 1s ease-out 1.2s;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.xs-overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0.8;
  z-index: 0;
}

.xs-bg-black {
  background-color: #000000;
}

.xs-sidebar-group .content-inner .logo {
  position: relative;
  max-width: 172px;
  width: 100%;
  margin-bottom: 30px;
}

.xs-sidebar-group .content-inner h4 {
  position: relative;
  display: block;
  font-size: 20px;
  line-height: 30px;
  color: #fff;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 15px;
}

.xs-sidebar-group .content-inner .content-box {
  margin-bottom: 30px;
  padding-right: 15px;
}

.xs-sidebar-group .content-inner .content-box p {
  color: #ffffff;
}

.xs-sidebar-group .content-inner .form-inner .form-group {
  position: relative;
  margin-bottom: 20px;
}

.xs-sidebar-group .content-inner .form-inner .form-group:last-child {
  margin-bottom: 0px;
}

.xs-sidebar-group .content-inner .form-inner .form-group input[type="text"],
.xs-sidebar-group .content-inner .form-inner .form-group input[type="email"],
.xs-sidebar-group .content-inner .form-inner .form-group textarea {
  position: relative;
  display: block;
  width: 100%;
  height: 50px;
  font-size: 15px;
  padding: 10px 20px;
  color: #848484;
  border: none;
  outline: none;
  transition: all 500ms ease;
}

.xs-sidebar-group .content-inner .form-inner .form-group .form-inner__btn {
  border: none;
  outline: none;
  background-color: var(--tecture-white);
  color: var(--tecture-black);
  display: inline-block;
  font-size: 16px;
  font-weight: 600;
  padding: 13px 45px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.xs-sidebar-group
  .content-inner
  .form-inner
  .form-group
  .form-inner__btn:hover {
  color: var(--tecture-white);
}

.xs-sidebar-group
  .content-inner
  .form-inner
  .form-group
  .form-inner__btn:before {
  background-color: var(--tecture-base);
}

.xs-sidebar-group
  .content-inner
  .form-inner
  .form-group
  .form-inner__btn:after {
  background-color: var(--tecture-base);
}

.xs-sidebar-group .content-inner .form-inner .form-group textarea {
  resize: none;
  height: 120px;
}

.xs-sidebar-group .content-inner .form-inner .form-group input:focus,
.xs-sidebar-group .content-inner .form-inner .form-group textarea:focus {
  border-color: #00224f;
}

/*== checked Css Css ======*/
.checked-box1 {
  position: relative;
  display: block;
  min-height: 26px;
}

.checked-box1 input[type="checkbox"] {
  display: none;
}

.checked-box1 label {
  position: relative;
  display: inline-block;
  padding-left: 25px;
  color: var(--tecture-gray);
  font-size: 16px;
  line-height: 26px;
  font-weight: 400;
  cursor: pointer;
  font-family: var(--tecture-font);
}

.checked-box1 input[type="checkbox"] + label span {
  position: absolute;
  display: block;
  top: 5px;
  left: 0;
  width: 14px;
  height: 14px;
  vertical-align: middle;
  background-color: transparent;
  border: 1px solid #e3e4ea;
  cursor: pointer;
  border-radius: 4px;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.checked-box1 label span:before {
  position: absolute;
  top: 0px;
  left: 0px;
  bottom: 0;
  right: 0;
  content: "";
  width: 8px;
  height: 8px;
  background: var(--tecture-base);
  border-radius: 50%;
  margin: 2px auto 0px;
  transform: scale(0);
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
}

.checked-box1 input[type="checkbox"]:checked + label span {
  border-color: #e3e4ea;
}

.checked-box1 input[type="checkbox"]:checked + label span:before {
  transform: scale(1);
}

/*--------------------------------------------------------------
# Navigations One
--------------------------------------------------------------*/
.main-header {
  background: transparent;
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0;
  width: 100%;
  transition: all 500ms ease;
  z-index: 999;
}

.main-menu {
  position: relative;
  display: block;
}

.main-menu__wrapper {
  position: relative;
  display: block;
  z-index: 1;
}

.main-menu__wrapper-inner {
  position: relative;
  display: block;
  padding-left: 260px;
  z-index: 1;
}

.main-menu__logo {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0a0c09;
  max-width: 260px;
  width: 100%;
  z-index: 2;
}

.main-menu__top {
  position: relative;
  display: block;
}

.main-menu__top-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  background-color: var(--tecture-black);
  padding: 10px 30px;
}

.main-menu__contact-list {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu__contact-list li {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu__contact-list li + li {
  margin-left: 46px;
}

.main-menu__contact-list li:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: -23px;
  width: 1px;
  background-color: rgba(var(--tecture-white-rgb), 0.3);
}

.main-menu__contact-list li:first-child:before {
  display: none;
}

.main-menu__contact-list li .icon {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu__contact-list li .icon i {
  font-size: 20px;
  color: var(--tecture-base);
}

.main-menu__contact-list li .text {
  margin-left: 10px;
}

.main-menu__contact-list li .text p {
  font-size: 15px;
  font-weight: 500;
  color: var(--tecture-white);
}

.main-menu__contact-list li .text p a {
  color: var(--tecture-white);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.main-menu__contact-list li .text p a:hover {
  color: var(--tecture-base);
}

.main-menu__top-right {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu__top-time {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu__top-time-icon {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu__top-time-icon span {
  position: relative;
  display: inline-block;
  font-size: 20px;
  color: var(--tecture-base);
}

.main-menu__top-text {
  font-size: 15px;
  font-weight: 500;
  color: var(--tecture-white);
  margin-left: 10px;
}

.main-menu__social {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 40px;
}

.main-menu__social a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  background-color: rgba(var(--tecture-white-rgb), 0.1);
  border-radius: 50%;
  font-size: 15px;
  color: var(--tecture-white);
  overflow: hidden;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  z-index: 1;
}

.main-menu__social a:hover {
  color: var(--tecture-white);
  border: 1px solid rgba(var(--tecture-basw-rgb), 1);
}

.main-menu__social a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: var(--tecture-base);
  transform: scale(0.5);
  border-radius: 50%;
  opacity: 0;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  z-index: -1;
}

.main-menu__social a:hover::before {
  transform: scale(1);
  opacity: 1;
}

.main-menu__social a + a {
  margin-left: 5px;
}

.main-menu__bottom {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  background-color: var(--tecture-white);
  padding: 0 30px 0;
}

.main-menu__main-menu-box {
  position: relative;
  display: block;
}

.main-menu__search-and-btn-box {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 9px 0;
}

.main-menu__search-box {
  position: relative;
  display: flex;
  align-items: center;
  margin-right: 25px;
}

.main-menu__search {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 22px;
  color: var(--tecture-black);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.main-menu__search:hover {
  color: var(--tecture-base);
}

.main-menu__btn:hover {
  color: var(--tecture-white);
}

.main-menu__btn:before,
.main-menu__btn:after {
  background-color: var(--tecture-black);
}

.stricky-header .main-menu__top {
  display: none;
}

.stricky-header.main-menu {
  background-color: #0a0c09;
}

.main-menu .main-menu__list,
.main-menu .main-menu__list > li > ul,
.main-menu .main-menu__list > li > ul > li > ul,
.stricky-header .main-menu__list,
.stricky-header .main-menu__list > li > ul,
.stricky-header .main-menu__list > li > ul > li > ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: none;
}

@media (min-width: 1200px) {
  .main-menu .main-menu__list,
  .main-menu .main-menu__list > li > ul,
  .main-menu .main-menu__list > li > ul > li > ul,
  .stricky-header .main-menu__list,
  .stricky-header .main-menu__list > li > ul,
  .stricky-header .main-menu__list > li > ul > li > ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

.main-menu .main-menu__list > li,
.stricky-header .main-menu__list > li {
  padding-top: 27px;
  padding-bottom: 27px;
  position: relative;
}

.main-menu .main-menu__list > li + li,
.stricky-header .main-menu__list > li + li {
  margin-left: 25px;
}

.main-menu .main-menu__list > li > a,
.stricky-header .main-menu__list > li > a {
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: var(--tecture-black);
  font-weight: 700;
  padding-left: 7px;
  padding-right: 7px;
  text-transform: capitalize;
  position: relative;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  z-index: 1;
}

.main-menu .main-menu__list > li.current > a,
.main-menu .main-menu__list > li:hover > a,
.stricky-header .main-menu__list > li.current > a,
.stricky-header .main-menu__list > li:hover > a {
  color: var(--tecture-base);
}

.main-menu .main-menu__list > li > a::before,
.stricky-header .main-menu__list > li > a::before {
  content: "";
  height: 33px;
  border-radius: 0px;
  background-color: var(--tecture-base);
  position: absolute;
  bottom: -3px;
  left: 0px;
  right: 0px;
  transition: transform 500ms ease;
  transform: scale(0, 1);
  transform-origin: left center;
  opacity: 0.2;
  border-radius: 5px;
  z-index: -1;
}

.main-menu .main-menu__list > li.current > a::before,
.main-menu .main-menu__list > li:hover > a::before,
.stricky-header .main-menu__list > li.current > a::before,
.stricky-header .main-menu__list > li:hover > a::before {
  transform: scale(1, 1);
  transform-origin: right center;
}

.main-menu .main-menu__list > li.dropdown > a {
  padding-right: 20px;
}

.main-menu .main-menu__list > li.dropdown > a:after {
  position: absolute;
  top: 52%;
  right: 7px;
  font-family: "Font Awesome 5 Free";
  content: "\f107";
  font-size: 14px;
  color: var(--tecture-black);
  transform: translateY(-50%);
  font-weight: 700;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  z-index: 1;
}

.main-menu .main-menu__list > li.current > a::after,
.main-menu .main-menu__list > li:hover > a::after,
.stricky-header .main-menu__list > li.current > a::after,
.stricky-header .main-menu__list > li:hover > a::after {
  color: var(--tecture-base);
}

.main-menu .main-menu__list > li > ul,
.main-menu .main-menu__list > li > ul > li > ul,
.stricky-header .main-menu__list > li > ul,
.stricky-header .main-menu__list > li > ul > li > ul {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #fff;
  min-width: 250px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: left;
  opacity: 0;
  visibility: hidden;
  z-index: 99;
  transform: perspective(500px) rotatex(-20deg);
  transform-origin: 50% 0%;
  -webkit-box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 9%);
  -khtml-box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 9%);
  -moz-box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 9%);
  -ms-box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 9%);
  -o-box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 9%);
  box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 9%);
  -webkit-transition: all 300ms linear 0ms;
  -khtml-transition: all 300ms linear 0ms;
  -moz-transition: all 300ms linear 0ms;
  -ms-transition: all 300ms linear 0ms;
  -o-transition: all 300ms linear 0ms;
  transition: all 300ms linear 0ms;
}

.main-menu .main-menu__list > li:hover > ul,
.main-menu .main-menu__list > li > ul > li:hover > ul,
.stricky-header .main-menu__list > li:hover > ul,
.stricky-header .main-menu__list > li > ul > li:hover > ul {
  opacity: 1;
  visibility: visible;
  transform: perspective(500px) rotatex(0);
}

.main-menu .main-menu__list > li > ul > li > ul > li > ul,
.stricky-header .main-menu__list > li > ul > li > ul > li > ul {
  display: none;
}

.main-menu .main-menu__list > li > ul > li,
.main-menu .main-menu__list > li > ul > li > ul > li,
.stricky-header .main-menu__list > li > ul > li,
.stricky-header .main-menu__list > li > ul > li > ul > li {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 100%;
  flex: 1 1 100%;
  width: 100%;
  position: relative;
  border-bottom: 1px solid rgba(var(--tecture-black-rgb), 0.1);
}

.main-menu .main-menu__list > li > ul > li:last-child,
.main-menu .main-menu__list > li > ul > li > ul > li:last-child,
.stricky-header .main-menu__list > li > ul > li:last-child,
.stricky-header .main-menu__list > li > ul > li > ul > li:last-child {
  border-bottom: 0;
}

.main-menu .main-menu__list > li > ul > li + li,
.main-menu .main-menu__list > li > ul > li > ul > li + li,
.stricky-header .main-menu__list > li > ul > li + li,
.stricky-header .main-menu__list > li > ul > li > ul > li + li {
  border-top: 0;
  margin-top: 0px;
}

.main-menu .main-menu__list > li > ul > li > a,
.main-menu .main-menu__list > li > ul > li > ul > li > a,
.stricky-header .main-menu__list > li > ul > li > a,
.stricky-header .main-menu__list > li > ul > li > ul > li > a {
  position: relative;
  font-size: 16px;
  line-height: 30px;
  color: var(--tecture-black);
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 20px 10px;
  -webkit-transition: all 200ms linear 0ms;
  -khtml-transition: all 200ms linear 0ms;
  -moz-transition: all 200ms linear 0ms;
  -ms-transition: all 200ms linear 0ms;
  -o-transition: all 200ms linear 0ms;
  transition: all 200ms linear 0ms;
}

.main-menu .main-menu__list > li > ul > li:hover > a,
.main-menu .main-menu__list > li > ul > li > ul > li:hover > a,
.stricky-header .main-menu__list > li > ul > li:hover > a,
.stricky-header .main-menu__list > li > ul > li > ul > li:hover > a {
  background-color: var(--tecture-base);
  color: var(--tecture-white);
  padding-left: 25px;
}

.main-menu .main-menu__list > li > ul > li > ul,
.stricky-header .main-menu__list > li > ul > li > ul {
  top: 0;
  left: 100%;
}

.main-menu .main-menu__list li ul li > ul.right-align,
.stricky-header .main-menu__list li ul li > ul.right-align {
  top: 0;
  left: auto;
  right: 100%;
}

.stricky-header {
  position: fixed;
  z-index: 991;
  top: 0;
  left: 0;
  background-color: #fff;
  width: 100%;
  visibility: hidden;
  -webkit-transform: translateY(-120%);
  transform: translateY(-120%);
  -webkit-transition: visibility 500ms ease, -webkit-transform 500ms ease;
  transition: visibility 500ms ease, -webkit-transform 500ms ease;
  transition: transform 500ms ease, visibility 500ms ease;
  transition: transform 500ms ease, visibility 500ms ease,
    -webkit-transform 500ms ease;
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
}

@media (max-width: 1199px) {
  .stricky-header {
    display: none !important;
  }
}

.stricky-header.stricky-fixed {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  visibility: visible;
  box-shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.2);
}

.mobile-nav__buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: auto;
  margin-right: 10px;
}

@media (min-width: 1200px) {
  .mobile-nav__buttons {
    display: none;
  }
}

.mobile-nav__buttons a {
  font-size: 20px;
  color: var(--tecture-base);
  cursor: pointer;
}

.mobile-nav__buttons a + a {
  margin-left: 10px;
}

.mobile-nav__buttons a:hover {
  color: var(--tecture-base);
}

.main-menu .mobile-nav__toggler {
  position: relative;
  display: inline-block;
  font-size: 24px;
  color: var(--tecture-base);
  cursor: pointer;
  -webkit-transition: 500ms;
  transition: 500ms;
}

.main-menu .mobile-nav__toggler:hover {
  color: var(--tecture-black);
}

@media (min-width: 1200px) {
  .main-menu .mobile-nav__toggler {
    display: none;
  }
}

.main-slider-four {
  position: relative; /* Ensure this is set */
}

.main-slider-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  justify-content: space-between;
  padding: 0 80px;
  pointer-events: none;
}

.main-slider-nav2 {
  position: absolute;
  top: 60%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  justify-content: space-between;
  padding: 0 80px;
  pointer-events: none;
}

@media only screen and (min-width: 375px) and (max-width: 1590px) {
  .main-slider-nav {
    padding: 0 20px;
  }

  .main-slider-nav2 {
    padding: 0 20px;
  }
}

.main-slider-nav .swiper-arrow-prev,
.main-slider-nav .swiper-arrow-next {
  pointer-events: auto; /* Re-enable pointer events for the buttons */
  /* Add your button styling here (background, color, size, etc.) */
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.5); /* Example background */
  color: white; /* Example icon color */
  width: 50px; /* Increased width */
  height: 50px; /* Increased height */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%; /* Make them circular */
  font-size: 24px; /* Increased icon size */
  transition: background-color 0.3s ease;
}

.main-slider-nav .swiper-arrow-prev:hover,
.main-slider-nav .swiper-arrow-next:hover {
  background-color: rgba(0, 0, 0, 0.8); /* Darker on hover */
}

.main-slider-nav2 .swiper-arrow-prev,
.main-slider-nav2 .swiper-arrow-next {
  pointer-events: auto; /* Re-enable pointer events for the buttons */
  /* Add your button styling here (background, color, size, etc.) */
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.5); /* Example background */
  color: white; /* Example icon color */
  width: 50px; /* Increased width */
  height: 50px; /* Increased height */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%; /* Make them circular */
  font-size: 24px; /* Increased icon size */
  transition: background-color 0.3s ease;
}

.main-slider-nav2 .swiper-arrow-prev:hover,
.main-slider-nav2 .swiper-arrow-next:hover {
  background-color: rgba(0, 0, 0, 0.8); /* Darker on hover */
}

.error-message {
  color: #829995;
  font-size: 14px;
  margin-bottom: 20px;
}
/*--------------------------------------------------------------
# Mobile Nav
--------------------------------------------------------------*/
.mobile-nav__wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  -webkit-transform-origin: left center;
  transform-origin: left center;
  -webkit-transition: visibility 500ms ease 500ms,
    -webkit-transform 500ms ease 500ms;
  transition: visibility 500ms ease 500ms, -webkit-transform 500ms ease 500ms;
  transition: transform 500ms ease 500ms, visibility 500ms ease 500ms;
  transition: transform 500ms ease 500ms, visibility 500ms ease 500ms,
    -webkit-transform 500ms ease 500ms;
  visibility: hidden;
}

.mobile-nav__wrapper .container {
  padding-left: 0;
  padding-right: 0;
}

.mobile-menu-visible .mobile-nav__wrapper {
  opacity: 1;
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  visibility: visible;
  -webkit-transition: visibility 500ms ease 0ms,
    -webkit-transform 500ms ease 0ms;
  transition: visibility 500ms ease 0ms, -webkit-transform 500ms ease 0ms;
  transition: transform 500ms ease 0ms, visibility 500ms ease 0ms;
  transition: transform 500ms ease 0ms, visibility 500ms ease 0ms,
    -webkit-transform 500ms ease 0ms;
}

.mobile-nav__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000;
  opacity: 0.5;
  cursor: pointer;
}

.mobile-nav__content {
  width: 300px;
  background-color: var(--tecture-black);
  z-index: 10;
  position: relative;
  height: 100%;
  overflow-y: auto;
  padding-top: 30px;
  padding-bottom: 30px;
  padding-left: 15px;
  padding-right: 15px;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  -webkit-transition: opacity 500ms ease 0ms, visibility 500ms ease 0ms,
    -webkit-transform 500ms ease 0ms;
  transition: opacity 500ms ease 0ms, visibility 500ms ease 0ms,
    -webkit-transform 500ms ease 0ms;
  transition: opacity 500ms ease 0ms, visibility 500ms ease 0ms,
    transform 500ms ease 0ms;
  transition: opacity 500ms ease 0ms, visibility 500ms ease 0ms,
    transform 500ms ease 0ms, -webkit-transform 500ms ease 0ms;
}

.mobile-menu-visible .mobile-nav__wrapper .mobile-nav__content {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateX(0);
  transform: translateX(0);
  -webkit-transition: opacity 500ms ease 500ms, visibility 500ms ease 500ms,
    -webkit-transform 500ms ease 500ms;
  transition: opacity 500ms ease 500ms, visibility 500ms ease 500ms,
    -webkit-transform 500ms ease 500ms;
  transition: opacity 500ms ease 500ms, visibility 500ms ease 500ms,
    transform 500ms ease 500ms;
  transition: opacity 500ms ease 500ms, visibility 500ms ease 500ms,
    transform 500ms ease 500ms, -webkit-transform 500ms ease 500ms;
}

.mobile-nav__content .logo-box {
  margin-bottom: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.mobile-nav__close {
  position: absolute;
  top: 20px;
  right: 15px;
  font-size: 18px;
  color: var(--tecture-white);
  cursor: pointer;
}

.mobile-nav__content .main-menu__list,
.mobile-nav__content .main-menu__list > li > ul,
.mobile-nav__content .main-menu__list > li > ul > li > ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.mobile-nav__content .main-menu__list > li > ul,
.mobile-nav__content .main-menu__list > li > ul > li > ul {
  display: none;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-left: 0.5em;
}

.mobile-nav__content .main-menu__list > li:not(:last-child),
.mobile-nav__content .main-menu__list > li > ul > li:not(:last-child),
.mobile-nav__content
  .main-menu__list
  > li
  > ul
  > li
  > ul
  > li:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav__content .main-menu__list > li > a > .main-menu-border {
  display: none !important;
}

.mobile-nav__content .main-menu__list > li > a,
.mobile-nav__content .main-menu__list > li > ul > li > a,
.mobile-nav__content .main-menu__list > li > ul > li > ul > li > a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  line-height: 30px;
  color: #ffffff;
  font-size: 14px;
  font-family: var(--tecture-font);
  font-weight: 500;
  height: 46px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 500ms;
  transition: 500ms;
}

.mobile-nav__content
  .main-menu__list
  > li
  > a.expanded
  .mobile-nav__content
  .main-menu__list
  > li
  > ul
  > li
  > a.expanded
  .mobile-nav__content
  .main-menu__list
  > li
  > ul
  > li
  > ul
  > li
  > a.expanded {
  color: var(--tecture-base);
}

.mobile-nav__content .main-menu__list li a.expanded {
  color: var(--tecture-base);
}

.mobile-nav__content .main-menu__list > li > button,
.mobile-nav__content .main-menu__list > li > ul > li > button,
.mobile-nav__content .main-menu__list > li > ul > li > ul > li > button {
  width: 30px;
  height: 30px;
  background-color: var(--tecture-base);
  border: none;
  outline: none;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  -webkit-transition: -webkit-transform 500ms ease;
  transition: -webkit-transform 500ms ease;
  transition: transform 500ms ease;
  transition: transform 500ms ease, -webkit-transform 500ms ease;
  padding: 0;
  position: absolute;
  right: 0;
  top: 8px;
}

.mobile-nav__content .main-menu__list > li > a > button.expanded,
.mobile-nav__content .main-menu__list > li > ul > li > a > button.expanded,
.mobile-nav__content
  .main-menu__list
  > li
  > ul
  > li
  > ul
  > li
  > a
  > button.expanded {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
  background-color: #fff;
  color: var(--tecture-base);
}

/* no menu after 2rd level dropdown */
.mobile-nav__content .main-menu__list > li > ul > li > ul > li > a > button,
.mobile-nav__content .main-menu__list > li > ul > li > ul > li > ul {
  display: none !important;
}

.mobile-nav__content .main-menu__list li.cart-btn span {
  position: relative;
  top: auto;
  right: auto;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}

.mobile-nav__content .main-menu__list li.cart-btn i {
  font-size: 16px;
}

.mobile-nav__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 30px;
}

.mobile-nav__top .main-menu__login a {
  color: var(--tecture-text-dark);
}

.mobile-nav__container {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav__social {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.mobile-nav__social a {
  font-size: 16px;
  color: var(--tecture-white);
  -webkit-transition: 500ms;
  transition: 500ms;
}

.mobile-nav__social a + a {
  margin-left: 30px;
}

.mobile-nav__social a:hover {
  color: var(--tecture-base);
}

.mobile-nav__contact {
  margin-bottom: 0;
  margin-top: 20px;
  margin-bottom: 20px;
}

.mobile-nav__contact li {
  color: var(--tecture-text-dark);
  font-size: 14px;
  font-weight: 500;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.mobile-nav__contact li + li {
  margin-top: 15px;
}

.mobile-nav__contact li a {
  color: #ffffff;
  -webkit-transition: 500ms;
  transition: 500ms;
}

.mobile-nav__contact li a:hover {
  color: var(--tecture-base);
}

.mobile-nav__contact li > i {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--tecture-base);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
  font-size: 11px;
  margin-right: 10px;
  color: #fff;
}

.mobile-nav__container .main-logo,
.mobile-nav__container .topbar__buttons,
.mobile-nav__container .main-menu__language,
.mobile-nav__container .main-menu__login {
  display: none;
}

/*--------------------------------------------------------------
# Navigations Two
--------------------------------------------------------------*/
.main-header-two {
  position: relative;
  display: block;
  width: 100%;
  background: #072b31;
  transition: all 500ms ease;
  z-index: 999;
}

.main-menu-two__wrapper {
  position: relative;
  display: block;
  z-index: 1;
}

.main-menu-two__wrapper-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 100px 0;
  z-index: 1;
}

.main-menu-two__logo {
  position: relative;
  display: block;
  padding: 34px 0;
}

.main-menu-two__left {
  position: relative;
  display: block;
}

.main-menu-two__right {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 130px;
}

.main-menu-two__main-menu-box {
  position: relative;
  display: block;
}

.main-menu-two__search-and-nav-sidebar-icon {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 12.5px 22.5px;
  gap: 20px;
  border-radius: 10px;
}

.main-menu-two__search {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  width: 45px;
  border: 1px solid var(--tecture-white);
  border-radius: 50%;
  font-size: 16px;
  color: var(--tecture-white);
}

.main-menu-two__search:hover {
  background-color: var(--tecture-black);
  border: 1px solid var(--tecture-black);
  color: var(--tecture-white);
}

.main-menu-two__nav-sidebar-icon {
  position: relative;
  display: block;
}

.main-menu-two__nav-sidebar-icon a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  width: 45px;
  border: 1px solid var(--tecture-white);
  border-radius: 50%;
  font-size: 16px;
  color: var(--tecture-white);
}

.main-menu-two__nav-sidebar-icon a:hover {
  background-color: var(--tecture-black);
  border: 1px solid var(--tecture-black);
  color: var(--tecture-white);
}

.main-menu-two .main-menu__list > li > a,
.stricky-header.main-menu-two .main-menu__list > li > a {
  color: var(--tecture-white);
  font-size: 17px;
  letter-spacing: 0.05rem;
}

.main-menu-two .main-menu__list > li.current > a,
.main-menu-two .main-menu__list > li:hover > a,
.stricky-header.main-menu-two .main-menu__list > li.current > a,
.stricky-header.main-menu-two .main-menu__list > li:hover > a {
  color: var(--tecture-base);
}

.main-menu-two .main-menu__list > li,
.stricky-header.main-menu-two .main-menu__list > li {
  padding-top: 20px;
  padding-bottom: 20px;
}

.main-menu-two .main-menu__list > li.dropdown > a:after {
  color: var(--tecture-white);
}

.main-menu-two .main-menu__list > li.current > a::after,
.main-menu.main-menu-two .main-menu__list > li:hover > a::after,
.stricky-header.main-menu-two .main-menu__list > li.current > a::after,
.stricky-header.main-menu-two .main-menu__list > li:hover > a::after {
  color: var(--tecture-base);
}

.stricky-header.main-menu-two {
  background-color: var(--tecture-black);
}

/*--------------------------------------------------------------
# Navigations Three
--------------------------------------------------------------*/
.main-header-three {
  position: relative;
  display: block;
  width: 100%;
  background: transparent;
  transition: all 500ms ease;
  z-index: 999;
}

.main-menu-three__top {
  position: relative;
  display: block;
  background-color: var(--tecture-black);
}

.main-menu-three__top-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 9px 0px 9px;
}

.main-menu-three__contact-list {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-three__contact-list li {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-three__contact-list li + li {
  margin-left: 50px;
}

.main-menu-three__contact-list li .icon {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-three__contact-list li .icon i {
  font-size: 16px;
  color: var(--tecture-base);
}

.main-menu-three__contact-list li .text {
  margin-left: 10px;
}

.main-menu-three__contact-list li .text p {
  font-size: 16px;
  color: var(--tecture-white);
}

.main-menu-three__contact-list li .text p a {
  color: var(--tecture-white);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.main-menu-three__contact-list li .text p a:hover {
  color: var(--tecture-base);
}

.main-menu-three__top-right {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu-three__social {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-three__social a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: var(--tecture-white);
  overflow: hidden;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  z-index: 1;
}

.main-menu-three__social a:hover {
  color: var(--tecture-base);
}

.main-menu-three__social a + a {
  margin-left: 25px;
}

.main-menu-three__wrapper {
  position: relative;
  display: block;
  z-index: 1;
}

.main-menu-three__wrapper-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0px 0px 0px;
  z-index: 1;
}

.main-menu-three__logo {
  position: relative;
  display: block;
}

.main-menu-three__logo a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 33px 0px 33px;
}

.main-menu-three__left {
  position: relative;
  display: block;
}

.main-menu-three__main-menu-box {
  position: relative;
  display: block;
}

.main-menu-three__right {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu-three__search-btn-box {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 40px;
  padding: 20px 0;
}

.main-menu-three__search {
  position: relative;
  display: flex;
  align-items: center;
  color: var(--tecture-black);
  font-size: 21px;
}

.main-menu-three__search:hover {
  color: var(--tecture-base);
}

.main-menu-three .main-menu__list > li,
.stricky-header.main-menu-three .main-menu__list > li {
  padding-top: 34px;
  padding-bottom: 34px;
}

.stricky-header.main-menu-three {
  background-color: var(--tecture-white);
}

/*--------------------------------------------------------------
# Navigations Four
--------------------------------------------------------------*/
.main-header-four {
  position: relative;
  display: block;
  width: 100%;
  background: transparent;
  transition: all 500ms ease;
  z-index: 999;
}

.main-menu-four__wrapper {
  position: relative;
  display: block;
  box-shadow: 0px 4.4px 20px -1px rgba(19, 16, 34, 0.05);
  z-index: 1;
}

.main-menu-four__wrapper-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0px 0px 0px;
  z-index: 1;
}

.main-menu-four__logo {
  position: relative;
  display: block;
}

.main-menu-four__logo a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 33px 0px 33px;
}

.main-menu-four__left {
  position: relative;
  display: block;
}

.main-menu-four__main-menu-box {
  position: relative;
  display: block;
}

.main-menu-four__right {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu-four__search-btn-box {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 40px;
  padding: 20px 0;
}

.main-menu-four__search {
  position: relative;
  display: flex;
  align-items: center;
  color: var(--tecture-black);
  font-size: 21px;
}

.main-menu-four__search:hover {
  color: var(--tecture-base);
}

.main-menu-four .main-menu__list > li,
.stricky-header.main-menu-four .main-menu__list > li {
  padding-top: 34px;
  padding-bottom: 34px;
}

.stricky-header.main-menu-four {
  background-color: var(--tecture-white);
}

/*--------------------------------------------------------------
# Navigations Four
--------------------------------------------------------------*/
.main-header-five {
  position: relative;
  display: block;
  width: 100%;
  background: transparent;
  transition: all 500ms ease;
  z-index: 999;
}

.main-header-five__wrapper {
  position: relative;
  display: block;
  z-index: 1;
}

.main-header-five__logo {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
}

.main-header-five__logo a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--tecture-black);
  padding: 48px 75px;
}

.main-menu-five__top {
  position: relative;
  display: block;
  background-color: var(--tecture-black);
}

.main-menu-five__top-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 9px 375px 9px;
  padding-right: 75px;
}

.main-menu-five__contact-list {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-five__contact-list li {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-five__contact-list li + li {
  margin-left: 50px;
}

.main-menu-five__contact-list li .icon {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-five__contact-list li .icon i {
  font-size: 16px;
  color: var(--tecture-base);
}

.main-menu-five__contact-list li .text {
  margin-left: 10px;
}

.main-menu-five__contact-list li .text p {
  font-size: 16px;
  color: var(--tecture-white);
}

.main-menu-five__contact-list li .text p a {
  color: var(--tecture-white);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.main-menu-five__contact-list li .text p a:hover {
  color: var(--tecture-base);
}

.main-menu-five__top-right {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu-five__social-box {
  position: relative;
  display: block;
  z-index: 1;
}

.main-menu-five__social-box:before {
  content: "";
  position: absolute;
  top: -5px;
  left: -30px;
  bottom: -5px;
  width: 1px;
  background-color: rgba(var(--tecture-white-rgb), 0.2);
}

.main-menu-five__social-box-inner {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 25px;
}

.main-menu-five__social-box-title {
  font-size: 16px;
  font-weight: 400;
  line-height: 16px;
  color: var(--tecture-white);
  font-family: var(--tecture-font);
}

.main-menu-five__social {
  position: relative;
  display: flex;
  align-items: center;
}

.main-menu-five__social a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: var(--tecture-white);
  overflow: hidden;
  -webkit-transition: all 0.4s linear;
  -o-transition: all 0.4s linear;
  transition: all 0.4s linear;
  z-index: 1;
}

.main-menu-five__social a:hover {
  color: var(--tecture-base);
}

.main-menu-five__social a + a {
  margin-left: 25px;
}

.main-menu-five__wrapper {
  position: relative;
  display: block;
  z-index: 1;
}

.main-menu-five__wrapper-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 0 75px 0;
  padding-left: 375px;
  z-index: 1;
}

.main-menu-five__left {
  position: relative;
  display: block;
}

.main-menu-five__main-menu-box {
  position: relative;
  display: block;
}

.main-menu-five__right {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.main-menu-five__search-btn-box {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 40px;
  padding: 10px 0;
}

.main-menu-five__search {
  position: relative;
  display: flex;
  align-items: center;
  color: var(--tecture-black);
  font-size: 21px;
}

.main-menu-five__search:hover {
  color: var(--tecture-base);
}

.stricky-header.main-menu-five {
  background-color: var(--tecture-white);
}

.stricky-header .main-menu-five__wrapper-inner {
  padding: 0 75px 0;
  padding-left: 75px;
}

/*--------------------------------------------------------------
# Search Popup
--------------------------------------------------------------*/
.search-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  padding-left: 20px;
  padding-right: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transform: translateY(-110%);
  transform: translateY(-110%);
  -webkit-transition: opacity 500ms ease, -webkit-transform 500ms ease;
  transition: opacity 500ms ease, -webkit-transform 500ms ease;
  transition: transform 500ms ease, opacity 500ms ease;
  transition: transform 500ms ease, opacity 500ms ease,
    -webkit-transform 500ms ease;
}

.search-popup.active {
  -webkit-transform: translateY(0%);
  transform: translateY(0%);
}

.search-popup__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--tecture-black);
  opacity: 0.75;
  cursor: pointer;
}

.search-popup__content {
  width: 100%;
  max-width: 560px;
}

.search-popup__content form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  position: relative;
  background-color: #fff;
  border-radius: 5px;
  overflow: hidden;
}

.search-popup__content form input[type="search"],
.search-popup__content form input[type="text"] {
  width: 100%;
  background-color: #fff;
  font-size: 16px;
  border: none;
  outline: none;
  height: 66px;
  padding-left: 30px;
}

.search-popup__content .thm-btn {
  padding: 0;
  width: 68px;
  height: 68px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
  position: absolute;
  top: 0;
  right: -1px;
  border-radius: 0;
  background-color: var(--tecture-base);
  border: 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.search-popup__content .thm-btn:after {
  background-color: var(--tecture-black);
  border-radius: 0;
}

.search-popup__content .thm-btn:before {
  background-color: var(--tecture-black);
  border-radius: 0;
}

.search-popup__content .thm-btn i {
  height: auto;
  width: auto;
  background-color: transparent;
  border-radius: 50%;
  color: var(--tecture-white);
  font-size: 22px;
  line-height: inherit;
  text-align: center;
  top: 0;
  margin-right: 0;
  padding-left: 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.search-popup__content .thm-btn:hover i {
  color: var(--tecture-white);
}

/*--------------------------------------------------------------
# End Header Css
--------------------------------------------------------------*/

/*** 
=============================================
    Welcome Style1 
=============================================
***/
.welcome-style1 {
  padding: 120px 0 120px;
}

.welcome-style1 .section-title {
  margin-bottom: 50px;
}

.welcome-style1__content {
  position: relative;
  display: block;
  padding-right: 20px;
}

.welcome-style1__content .title {
  position: relative;
  display: block;
  padding-bottom: 21px;
}

.welcome-style1__content .title h3 {
  font-size: 22px;
  font-weight: 500;
  line-height: 32px;
}

.welcome-style1__content .text {
  position: relative;
  display: block;
}

.welcome-style1__content .text p + p {
  margin-top: 21px;
}

.welcome-style1__content .bottom-box {
  position: relative;
  display: flex;
  align-items: center;
  padding-top: 36px;
}

.welcome-style1__content .bottom-box .btn-box {
  position: relative;
  display: block;
  line-height: 0;
}

.welcome-style1__content .bottom-box .mail-box {
  position: relative;
  display: block;
  flex: 1;
  margin-left: 30px;
}

.welcome-style1__content .bottom-box .mail-box h3 {
  font-size: 22px;
  line-height: 28px;
}

.welcome-style1__content .bottom-box .mail-box h3 a {
  color: var(--tecture-base);
  transition: all 200ms linear;
  transition-delay: 0.1s;
}

.welcome-style1__content .bottom-box .mail-box h3 a:hover {
  color: var(--tecture-white);
}

.welcome-style1__img {
  position: relative;
  display: block;
  padding-top: 0px;
}

.welcome-style1__img .row {
  --bs-gutter-x: 0px;
}

.welcome-style1__img .img1 {
  position: relative;
  display: block;
  overflow: hidden;
  /* margin-top: -30px; */
  z-index: 5;
}

.welcome-style1__img .img1::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--tecture-black);
  opacity: 0;
  transition: background-color 0.5s ease;
  transition: all 0.5s ease;
  z-index: 1;
}

.welcome-style1__img .img1:hover::before {
  opacity: 0.5;
}

.welcome-style1__img .img1 img {
  width: 100%;
  transform: scale3d(1, 1, 1);
  transition: transform 1s ease-in-out;
}

.welcome-style1__img .img1:hover img {
  transform: scale(1.05) rotate(0deg);
}

.welcome-style1__img .img1 .video-btn {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.welcome-style1__img .img1 .video-btn .video-popup {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--tecture-base);
  color: var(--tecture-white);
  font-size: 25px;
  line-height: 20px;
  transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -webkit-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
}

.welcome-style1__img .img1 .video-btn .video-popup:hover {
  background-color: var(--tecture-white);
  color: var(--tecture-base);
}

.welcome-style1__img .img1 .video-btn .video-popup::before,
.welcome-style1__img .img1 .video-btn .video-popup::after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -ms-box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.7);
  -o-box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.7);
  -webkit-box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.7);
  box-shadow: 0 0 0 0 rgba(var(--tecture-white-rgb), 0.67);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
}

.welcome-style1__img .img1 .video-btn .video-popup::before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
}

.welcome-style1__img .img1 .video-btn .video-popup:after {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
}

.welcome-style1__img .img2 {
  position: relative;
  display: block;
  overflow: hidden;
  margin-top: -94px;
  margin-right: -30px;
}

.welcome-style1__img .img2::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--tecture-black);
  opacity: 0;
  transition: background-color 0.5s ease;
  transition: all 0.5s ease;
  z-index: 1;
}

.welcome-style1__img .img2:hover::before {
  opacity: 0.5;
}

.welcome-style1__img .img2 img {
  width: 100%;
  transform: scale3d(1, 1, 1);
  transition: transform 1s ease-in-out;
}

.welcome-style1__img .img2:hover img {
  transform: scale(1.05) rotate(0deg);
}

/*** 
=============================================
    Service Style4
=============================================
***/
.service-style4 {
  position: relative;
  display: block;
  padding: 120px 0px 120px;
  z-index: 1;
}

.single-service-style4 {
  position: relative;
  display: block;
  margin-bottom: 0;
}

.single-service-style4 .img-box {
  position: relative;
  display: block;
  overflow: hidden;
}

.single-service-style4 .img-box::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--tecture-black);
  opacity: 0;
  transition: background-color 0.5s ease;
  transition: all 0.5s ease;
  z-index: 1;
}

.single-service-style4:hover .img-box::before {
  opacity: 0.6;
}

.single-service-style4 .img-box img {
  width: 100%;
  transform: scale3d(1, 1, 1);
  transition: transform 1s ease-in-out;
}

.single-service-style4:hover .img-box img {
  transform: scale(1.05) rotate(0deg);
}

.single-service-style4 .img-box .overlay-icon {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  opacity: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(70px);
  transition: all 0.5s ease-in-out 0s;
  z-index: 5;
}

.single-service-style4:hover .img-box .overlay-icon {
  opacity: 1;
  transform: translateY(0px);
}

.single-service-style4 .img-box .overlay-icon a {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgb(0, 0, 0, 0.6);
  color: var(--tecture-white);
  font-size: 20px;
  transition: all 200ms linear;
  transition-delay: 0.1s;
}

.single-service-style4 .img-box .overlay-icon a:hover {
  color: var(--tecture-white);
  background-color: var(--tecture-base);
}

.single-service-style4 .content-box {
  position: relative;
  display: block;
  padding-top: 22px;
}

.single-service-style4 .content-box h3 {
  font-size: 20px;
  line-height: 28px;
  text-transform: capitalize;
  margin-bottom: 7px;
}

.single-service-style4 .content-box h3 a {
  color: var(--tecture-white);
  transition: all 200ms linear;
  transition-delay: 0.1s;
}

.single-service-style4 .content-box h3 a:hover {
  color: var(--tecture-base);
}

.single-service-style4 .content-box p {
  margin: 0;
}

.service-style4__carousel.owl-theme {
  position: relative;
  display: block;
  overflow: hidden;
}

.service-style4__carousel.owl-theme .owl-nav {
  position: relative;
  display: block;
  overflow: hidden;
  margin-top: 53px;
}

.service-style4__carousel.owl-theme .owl-nav .owl-next {
  height: 60px;
  width: 60px;
  line-height: 82px;
  border-radius: 0;
  color: var(--tecture-white);
  background-color: var(--tecture-base);
  font-size: 22px;
  margin: 0;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.service-style4__carousel.owl-theme .owl-nav .owl-prev {
  height: 60px;
  width: 60px;
  line-height: 82px;
  border-radius: 0;
  color: var(--tecture-white);
  background-color: var(--tecture-base);
  font-size: 22px;
  margin: 0;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.service-style4__carousel.owl-theme .owl-nav .owl-next {
  margin-left: 10px;
}

.service-style4__carousel.owl-theme .owl-nav .owl-prev {
  margin-right: 10px;
}

.service-style4__carousel.owl-theme .owl-nav .owl-next span,
.service-style4__carousel.owl-theme .owl-nav .owl-prev span {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-style4__carousel.owl-theme .owl-nav .owl-next:hover,
.service-style4__carousel.owl-theme .owl-nav .owl-prev:hover {
  background-color: var(--tecture-white);
  color: var(--tecture-base);
}

.service-style4__carousel.owl-carousel .owl-stage-outer {
  overflow: visible;
}

.service-style4_carousel.owl-carousel .owl-item {
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease, visibility 500ms ease;
}

.service-style4__carousel.owl-carousel .owl-item.active {
  opacity: 1;
  visibility: visible;
}

/*** 
=============================================
    slogan-one
=============================================
***/
.slogan-one {
  position: relative;
  display: block;
  overflow: hidden;
  padding: 110px 0px 120px;
  z-index: 10;
}

.slogan-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-position: center center;
  mix-blend-mode: luminosity;
  z-index: -1;
}

.slogan-one__bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  opacity: 0.9;
  background-color: var(--tecture-black);
}

.whychoose__bg {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-position: center center;
  mix-blend-mode: luminosity;
  z-index: -1;
}

.whychoose__bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  opacity: 0.9;
  background-color: #3f2021;
}

.slogan-one__content {
  position: relative;
  display: block;
}

.slogan-one__content .title {
  position: relative;
  display: block;
}

.slogan-one__content .title h3 {
  color: var(--tecture-white);
  font-size: 30px;
  line-height: 40px;
  margin-bottom: 8px;
}

.slogan-one__content .title h4 {
  color: var(--tecture-white);
  font-size: 25px;
  line-height: 35px;
  margin-bottom: 25px;
}

.slogan-one__content .title p {
  margin: 0;
}

.slogan-one__content .btn-box {
  position: relative;
  display: block;
  line-height: 0;
  padding-top: 53px;
}

.slogan-one__content .btn-box .thm-btn:before {
  width: 51%;
}

.main-slider__text {
  position: relative;
  width: 50%;
  margin: 0 auto;
}

.locale-selector {
  background: none;
  border: none;
  color: var(--tecture-white);
}
/*--------------------------------------------------------------
# End
--------------------------------------------------------------*/

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .feature-one__list li .text {
    margin-left: 20px;
  }

  .feature-one__list li .text p {
    font-size: 18px;
  }

  .about-one__left {
    margin-right: -320px;
  }

  .about-one__img-list li:nth-child(1) {
    margin-top: 0;
  }

  .about-one__img-list li:nth-child(2) {
    margin-top: 0;
  }

  .about-one__right {
    margin-top: 55px;
  }

  .about-one__content {
    padding: 60px 40px 60px;
  }

  .projects-one__top-inner {
    flex-direction: column;
    align-items: baseline;
  }

  .projects-one__filter-box {
    top: 0;
    margin-top: 38px;
  }

  .blog-one__content {
    padding: 46px 20px 35px;
  }

  .footer-widget__usefull-link {
    margin-left: 20px;
  }

  .footer-widget__link-2 {
    margin-left: 30px;
  }

  .footer-widget__services {
    margin-left: -16px;
  }

  .feature-two__single {
    padding: 65px 50px 65px;
  }

  .testimonial-two__single {
    padding: 79px 20px 43px;
  }

  .contact-one__title {
    font-size: 35px;
    line-height: 45px;
  }

  .contact-one__title br {
    display: none;
  }

  .contact-one__contact-info {
    margin-left: 0;
  }

  .how-it-work__single {
    margin-bottom: 30px;
  }

  .how-it-work__icon {
    position: relative;
    top: 0;
    right: 0;
    margin-top: 20px;
  }

  .how-it-work__single-2 .how-it-work__icon {
    left: 0;
  }

  .how-it-work__single-2 {
    padding-left: 0;
  }

  .services-four__icon-box {
    padding: 0 15px 50px;
    padding-right: 0;
  }

  .services-four__content {
    padding: 41px 15px 0;
  }

  .section-title__title {
    font-size: 40px;
    line-height: 50px;
  }
}

/* Medium screen  */
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .auto-container {
    padding: 0px 15px;
  }

  .feature-one__list li .text {
    margin-left: 15px;
  }

  .feature-one__list li .text p {
    font-size: 15px;
  }

  .about-one__left {
    margin-right: 0;
  }

  .about-one__right {
    margin-top: 60px;
  }

  .services-one__content-box {
    padding: 41px 25px 24px;
  }

  .projects-one__top-inner {
    flex-direction: column;
    align-items: baseline;
  }

  .projects-one__filter-box {
    top: 0;
    margin-top: 38px;
  }

  .design-interior__left {
    margin-right: 0;
  }

  .design-interior__right {
    max-width: 600px;
    margin: 60px auto 0;
  }

  .video-one__thumb-box {
    right: 50px;
  }

  .awards-one__left {
    max-width: 600px;
    margin: 0 auto 60px;
  }

  .awards-one__right {
    max-width: 600px;
    margin: 0 auto 0;
  }

  .awards-one {
    padding: 120px 0 120px;
  }

  .blog-one__content {
    padding: 46px 15px 35px;
  }

  .blog-one__title a br {
    display: none;
  }

  .footer-widget__usefull-link {
    margin-left: 0;
  }

  .footer-widget__services {
    margin-left: 0;
    margin-top: 35px;
  }

  .footer-widget__instagram {
    margin-left: 0;
    margin-top: 35px;
  }

  .feature-two__single {
    padding: 65px 15px 65px;
  }

  .about-two__left {
    max-width: 650px;
    margin: 0 auto 0;
  }

  .about-two__left .section-title__title {
    font-size: 50px;
  }

  .about-two__right {
    max-width: 650px;
    margin: 60px auto 0;
  }

  .project-two__content {
    flex-direction: column;
    align-items: baseline;
  }

  .project-two__arrow {
    margin-top: 15px;
  }

  .services-two__top {
    padding: 63px 15px 63px;
    padding-right: 15px;
  }

  .services-two__top .section-title__title {
    font-size: 40px;
    line-height: 50px;
  }

  .services-two__single {
    padding: 45px 15px 45px;
    padding-right: 15px;
  }

  .services-two__single-right {
    gap: 25px;
  }

  .contact-one__left {
    max-width: 600px;
    margin: 0 auto 0;
  }

  .contact-one__right {
    max-width: 600px;
    margin: 60px auto 0;
  }

  .contact-one__contact-info {
    margin-top: 40px;
  }

  .blog-two .section-title__title {
    font-size: 40px;
    line-height: 50px;
  }

  .blog-two__title {
    font-size: 16px;
    line-height: 26px;
  }

  .about-three__left {
    max-width: 600px;
    margin: 0 auto 60px;
  }

  .about-three__right {
    max-width: 600px;
    margin: 0 auto 0;
  }

  .how-it-work__single {
    margin-bottom: 30px;
  }

  .how-it-work__icon {
    position: relative;
    top: 0;
    right: 0;
    margin-top: 20px;
  }

  .how-it-work__single-2 .how-it-work__icon {
    left: 0;
  }

  .how-it-work__single-2 {
    padding-left: 0;
  }

  .how-it-work__text br {
    display: none;
  }

  .services-four__icon-box {
    padding: 0 15px 50px;
    padding-right: 0;
    margin-right: 20px;
  }

  .services-four__content {
    padding: 41px 15px 0;
  }

  .services-four__title {
    font-size: 20px;
    line-height: 30px;
  }

  .contact-page__information {
    padding: 64px 15px 72px;
  }

  .contact-page__information-title {
    font-size: 35px;
  }

  .service-details__img-and-point-box {
    gap: 30px;
    flex-direction: column;
    align-items: baseline;
  }

  .team-details__left {
    max-width: 650px;
    margin: 0 auto 0;
  }

  .team-details__right {
    max-width: 650px;
    margin: 50px auto 0;
  }

  .team-details__address-box {
    margin-left: 0;
    padding: 39px 30px 43px;
  }

  .about-one__img-list {
    gap: 0px;
  }

  .about-one__content {
    padding: 30px 70px 0px;
    padding-right: 0;
  }

  .about-one__img-list li:nth-child(1) {
    margin-top: 0px;
  }

  .about-one__img-list li:nth-child(2) {
    margin-top: 0px;
  }

  .about-one__progress-list {
    max-width: 570px;
  }

  .blog-two-single .content-box .meta-box .meta-info {
    margin-left: 20px;
  }
}

/* Tablet Layout: 768px. */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .auto-container {
    padding: 0px 15px;
  }

  .feature-one__list {
    max-width: 400px;
    margin: 0 auto 0;
  }

  .feature-one__list li {
    flex: 0 0 100%;
    max-width: 100%;
    justify-content: inherit;
  }

  .feature-one__list li + li {
    margin-top: 20px;
  }

  .about-one__left {
    margin-right: 0;
  }

  .about-one__img-list {
    justify-content: center;
  }

  .about-one__img-list li:nth-child(1) {
    margin-top: 0;
  }

  .about-one__img-list li:nth-child(2) {
    margin-top: 0;
  }

  .about-one__right {
    margin-top: 60px;
  }

  .about-one__content {
    padding: 0px 70px 0px;
    border-top-left-radius: 20px;
  }

  .projects-one__top-inner {
    flex-direction: column;
    align-items: baseline;
  }

  .projects-one__filter-box {
    top: 0;
    margin-top: 38px;
  }

  .design-interior__left {
    margin-right: 0;
    padding: 101px 25px 29px;
  }

  .design-interior__right {
    max-width: 600px;
    margin: 60px auto 0;
  }

  .design-interior .section-title__title {
    font-size: 45px;
    line-height: 55px;
  }

  .testimonial-one__single {
    padding: 54px 20px 54px;
  }

  .video-one__thumb-box {
    right: 20px;
  }

  .awards-one__left {
    max-width: 600px;
    margin: 0 auto 60px;
  }

  .awards-one__right {
    max-width: 600px;
    margin: 0 auto 0;
  }

  .awards-one {
    padding: 120px 0 120px;
  }

  .blog-one .section-title__title {
    font-size: 50px;
  }

  .footer-widget__usefull-link {
    margin-left: 0;
  }

  .footer-widget__services {
    margin-left: 0;
    margin-top: 35px;
  }

  .footer-widget__instagram {
    margin-left: 0;
    margin-top: 35px;
  }

  .site-footer__bottom-inner {
    flex-direction: column;
  }

  .site-footer__bottom-text {
    margin-bottom: 10px;
  }

  .feature-two__single {
    border-left: 0;
  }

  .feature-two__single.last-child {
    border-right: 0;
  }

  .about-two__left {
    max-width: 650px;
    margin: 0 auto 0;
  }

  .about-two__left .section-title__title {
    font-size: 50px;
  }

  .about-two__right {
    max-width: 650px;
    margin: 60px auto 0;
  }

  .counter-one__list li {
    flex: 0 0 50%;
    max-width: 50%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .counter-one__title-box .section-title__title {
    font-size: 45px;
  }

  .project-two__content {
    padding: 12px 20px 12px;
    padding-right: 30px;
  }

  .project-two__title {
    font-size: 16px;
  }

  .project-two__arrow a {
    height: 40px;
    width: 40px;
    font-size: 15px;
  }

  .project-two__carousel.owl-theme .owl-nav {
    right: 0;
  }

  .project-two__content {
    flex-direction: column;
    align-items: baseline;
  }

  .project-two__arrow {
    margin-top: 15px;
  }

  .services-two__top {
    padding: 63px 15px 63px;
    padding-right: 15px;
    flex-direction: column;
    align-items: baseline;
  }

  .services-two__top .section-title__title {
    font-size: 40px;
    line-height: 50px;
  }

  .services-two__btn-box {
    margin-top: 30px;
  }

  .services-two__single {
    padding: 45px 15px 45px;
    padding-right: 15px;
  }

  .services-two__single-right {
    gap: 20px;
    flex-direction: column;
    align-items: baseline;
  }

  .services-two__text br {
    display: none;
  }

  .testimonial-two__single {
    padding: 79px 20px 43px;
  }

  .contact-one__left {
    max-width: 600px;
    margin: 0 auto 0;
  }

  .contact-one__right {
    max-width: 600px;
    margin: 50px auto 0;
  }

  .contact-one__contact-info {
    margin-top: 40px;
  }

  .blog-two .section-title__title {
    font-size: 40px;
    line-height: 50px;
  }

  .blog-two__title {
    font-size: 18px;
    line-height: 28px;
  }

  .blog-two__carousel.owl-theme .owl-nav {
    display: none;
  }

  .about-three__left {
    max-width: 600px;
    margin: 0 auto 60px;
  }

  .about-three__right {
    max-width: 600px;
    margin: 0 auto 0;
  }

  .how-it-work__single {
    margin-bottom: 30px;
  }

  .how-it-work__icon {
    position: relative;
    top: 0;
    right: 0;
    margin-top: 20px;
  }

  .how-it-work__single-2 .how-it-work__icon {
    left: 0;
  }

  .how-it-work__single-2 {
    padding-left: 0;
  }

  .how-it-work__text br {
    display: none;
  }

  .sidebar {
    margin-top: 20px;
  }

  .contact-page__left {
    margin-left: 15px;
    margin-right: 15px;
  }

  .contact-page__right {
    margin-left: 15px;
    margin-right: 15px;
    margin-top: 50px;
  }

  .united-kingdom__inner {
    flex-direction: column;
    gap: 15px;
  }

  .project-details__faq-box {
    margin-top: 30px;
  }

  .service-details__right {
    margin-top: 60px;
  }

  .team-details__left {
    max-width: 650px;
    margin: 0 auto 0;
  }

  .team-details__right {
    max-width: 650px;
    margin: 50px auto 0;
  }

  .team-details__address-box {
    margin-left: 0;
    padding: 39px 30px 43px;
  }

  .coming-soon-page {
    height: auto !important;
    padding: 110px 0 120px;
  }

  .coming-soon-page__content .big-title {
    font-size: 55px;
    line-height: 55px;
  }

  .coming-soon-page .timer-box .countdown-timer li {
    float: none;
  }

  .coming-soon-page .timer-box .countdown-timer li + li {
    margin-top: 20px;
  }

  .coming-soon-page__content {
    top: 0%;
    transform: translateY(0%);
  }

  .coming-soon-page__content .inner .text p br {
    display: none;
  }

  .coming-soon-page__subscribe-box form {
    flex-direction: column;
  }
}

/* Mobile Layout: 320px. */
@media only screen and (max-width: 767px) {
  .auto-container {
    padding: 0px 15px;
  }

  .section-title__title {
    font-size: 25px;
    line-height: 35px;
  }

  .section-title__title br {
    display: none;
  }

  .feature-one__list {
    max-width: 400px;
    margin: 0 auto 0;
  }

  .feature-one__list li {
    flex: 0 0 100%;
    max-width: 100%;
    justify-content: inherit;
  }

  .feature-one__list li + li {
    margin-top: 20px;
  }

  .feature-one__list li .text p {
    font-size: 15px;
  }

  .feature-one__list li .text p br {
    display: none;
  }

  .feature-one__list li .icon {
    max-width: 70px;
    width: 100%;
  }

  .about-one__left {
    margin-right: 0;
  }

  .about-one__img-list {
    justify-content: center;
  }

  .about-one__img-list li:nth-child(1) {
    margin-top: 0;
  }

  .about-one__img-list li:nth-child(2) {
    margin-top: 0;
  }

  .about-one__right {
    margin-top: 60px;
  }

  .about-one__content {
    padding: 60px 15px 60px;
  }

  .about-one__design-make {
    flex-direction: column;
    align-items: baseline;
  }

  .about-one__design-make-content {
    margin-left: 0;
    margin-top: 20px;
  }

  .about-one__design-make-text br {
    display: none;
  }

  .services-one__content-box {
    padding: 41px 25px 24px;
  }

  .projects-one__top-inner {
    flex-direction: column;
    align-items: center;
  }

  .projects-one__filter-box {
    top: 0;
    margin-top: 38px;
  }

  .projects-one__filter.style1 li {
    margin-bottom: 20px;
  }

  .projects-one__top {
    margin-bottom: 38px;
  }

  .projects-one__top-inner .section-title {
    text-align: center;
  }

  .design-interior__left {
    margin-right: 0;
    padding: 101px 25px 29px;
  }

  .design-interior__list li {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 40px;
  }

  .design-interior__right {
    max-width: 600px;
    margin: 60px auto 0;
  }

  .testimonial-one__single {
    padding: 54px 15px 54px;
  }

  .video-one__thumb-box {
    display: none;
  }

  .awards-one__left {
    margin-right: 0;
  }

  .awards-one__list li {
    flex-direction: column;
  }

  .awards-one__year {
    margin-top: 20px;
    margin-bottom: 25px;
  }

  .awards-one__right {
    margin-left: 0;
    margin-right: 0;
    margin-top: 60px;
  }

  .awards-one__img-two {
    display: none;
  }

  .blog-one__content {
    padding: 46px 15px 35px;
  }

  .blog-one__title a br {
    display: none;
  }

  .footer-widget__about {
    margin-top: 0;
  }

  .footer-widget__usefull-link {
    margin-left: 0;
    margin-top: 42px;
  }

  .footer-widget__link-2 {
    margin-left: 25px;
  }

  .footer-widget__services {
    margin-left: 0;
    margin-top: 35px;
  }

  .footer-widget__instagram {
    margin-left: 0;
    margin-top: 35px;
  }

  .site-footer__bottom-inner {
    flex-direction: column;
    text-align: center;
  }

  .site-footer__bottom-text {
    margin-bottom: 10px;
  }

  .site-footer__bottom-menu {
    flex-direction: column;
  }

  .top-menu li + li {
    margin-left: 0;
    margin-top: 10px;
  }

  .top-menu li:before {
    display: none;
  }

  .site-footer__bottom-menu li + li {
    margin-left: 0;
    margin-top: 10px;
  }

  .site-footer__bottom-menu li:before {
    display: none;
  }

  .feature-two__single {
    border-left: 0;
    padding: 40px 15px 40px;
  }

  .feature-two__single.last-child {
    border-right: 0;
  }

  .about-two__text br {
    display: none;
  }

  .about-two__history li {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .about-two__history li + li {
    margin-top: 20px;
  }

  .about-two__right {
    margin-left: 0;
    margin-top: 60px;
  }

  .counter-one__list {
    flex-direction: column;
  }

  .project-two__content {
    flex-direction: column;
    align-items: baseline;
  }

  .project-two__arrow {
    margin-top: 15px;
  }

  .project-two__carousel.owl-theme .owl-nav {
    display: none;
  }

  .services-two__top {
    padding: 63px 15px 63px;
    padding-right: 15px;
    flex-direction: column;
    align-items: baseline;
  }

  .services-two__btn-box {
    margin-top: 30px;
  }

  .services-two__single {
    padding: 45px 15px 45px;
    padding-right: 15px;
  }

  .services-two__single-right {
    gap: 20px;
    flex-direction: column;
    align-items: baseline;
  }

  .services-two__text br {
    display: none;
  }

  .services-two__single-left {
    gap: 20px;
    flex-direction: column;
    align-items: baseline;
    margin-bottom: 20px;
  }

  .services-two__wrapper {
    padding-left: 0;
  }

  .testimonial-two__single {
    padding: 79px 15px 43px;
  }

  .testimonial-two__text {
    font-size: 18px;
    line-height: 28px;
  }

  .testimonial-two__carousel.owl-theme .owl-nav {
    display: none;
  }

  .contact-one__left {
    margin-right: 0;
  }

  .contact-one__form {
    padding: 60px 10px 60px;
  }

  .contact-one__right {
    margin-top: 50px;
  }

  .contact-one__title {
    font-size: 25px;
    line-height: 35px;
  }

  .contact-one__title br {
    display: none;
  }

  .contact-one__contact-info {
    margin-top: 30px;
    margin-left: 0;
  }

  .blog-two__carousel.owl-theme .owl-nav {
    display: none;
  }

  .about-three__right {
    margin-right: 0;
    margin-left: 0;
    margin-top: 0px;
  }

  .about-three__left {
    margin-right: 0;
    margin-left: 0;
    margin-top: 30px;
  }

  .about-three__img {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
  }

  .about-three__experience-box {
    position: relative;
    right: 0;
    top: 0;
    margin-top: 30px;
  }

  .about-three__experience-box-left {
    position: relative;
    left: 0;
    top: 0;
    margin-top: 30px;
  }

  .how-it-work__single {
    margin-bottom: 30px;
  }

  .how-it-work__icon {
    position: relative;
    top: 0;
    right: 0;
    margin-top: 20px;
  }

  .how-it-work__single-2 .how-it-work__icon {
    left: 0;
  }

  .how-it-work__single-2 {
    padding-left: 0;
  }

  .how-it-work__text br {
    display: none;
  }

  .services-three__single {
    -webkit-border-start: 0px solid var(--tecture-bdr-color);
    border-inline-start: 0px solid var(--tecture-bdr-color);
    padding-left: 0;
  }

  .projects-three__carousel.owl-theme .owl-nav {
    padding: 0 20px;
  }

  .page-header__title-box p {
    font-size: 20px;
    line-height: 30px;
  }

  .page-header__title-box h3 {
    font-size: 30px;
    line-height: 40px;
  }

  .services-three__showcase {
    padding: 0 0 30px 29px;

    .services-four__icon-box {
      padding: 0 15px 50px;
      padding-right: 0;
      margin-right: 20px;
    }

    .services-four__content {
      padding: 41px 15px 0;
    }

    .services-four__title {
      font-size: 20px;
      line-height: 30px;
    }

    .sidebar {
      margin-top: 20px;
    }

    .blog-details__meta-and-share {
      padding-right: 0;
      flex-direction: column;
      align-items: baseline;
    }

    .blog-details__share {
      margin-top: 20px;
    }

    .blog-details__tag-box {
      gap: 10px;
      padding: 25px 15px 25px;
      flex-direction: column;
    }

    .blog-details__tag-list a {
      padding: 9px 15px 8px;
    }

    .blog-details__client-info {
      gap: 20px;
      flex-direction: column;
      align-items: baseline;
    }

    .blog-details__pagenation {
      flex-direction: column;
    }

    .blog-details__pagenation li + li {
      margin-top: 20px;
    }

    .comment-one__single {
      flex-direction: column;
      align-items: baseline;
    }

    .comment-one__content {
      margin-left: 0;
      margin-top: 20px;
    }

    .comment-one__single-2 {
      margin-left: 0;
    }

    .sidebar__post {
      padding: 49px 16px 76px;
    }

    .sidebar__post-list li {
      padding: 16px 15px 16px;
    }

    .sidebar__all-category {
      padding: 49px 15px 76px;
    }

    .have-any-query {
      padding: 58px 15px 67px;
    }

    .sidebar__tags {
      padding: 40px 15px 40px;
    }

    .contact-page__information {
      padding: 64px 15px 72px;
    }

    .contact-page__information-title {
      font-size: 35px;
    }

    .contact-page__information-list li {
      gap: 15px;
      padding: 21px 10px 20px;
      flex-direction: column;
      text-align: center;
    }

    .contact-page__left {
      margin-left: 15px;
      margin-right: 15px;
    }

    .contact-page__right {
      margin-left: 15px;
      margin-right: 15px;
      margin-top: 50px;
    }

    .united-kingdom__inner {
      flex-direction: column;
      gap: 15px;
    }

    .project-details__faq-box {
      margin-top: 30px;
    }

    .project-details__faq-box .faq-one-accrodion .accrodion-title {
      padding: 22px 15px 23px;
      padding-left: 30px;
    }

    .project-details__faq-box-count {
      left: 5px;
    }

    .project-details__faq-box .faq-one-accrodion .accrodion-content {
      padding-right: 15px;
    }

    .project-details__faq-box .faq-one-accrodion .accrodion-title h4 {
      font-size: 18px;
      line-height: 28px;
    }

    .service-details__right {
      margin-top: 60px;
    }

    .service-details__contact-box {
      padding: 68px 20px 71px;
    }

    .service-details__title-1 {
      font-size: 30px;
      line-height: 40px;
    }

    .service-details__img-and-point-box {
      gap: 30px;
      flex-direction: column;
      align-items: baseline;
    }

    .service-details__title-2 {
      font-size: 30px;
      line-height: 40px;
    }

    .team-details__left {
      margin-right: 0;
    }

    .team-details__right {
      margin-left: 0;
      margin-top: 40px;
    }

    .team-details__progress-title {
      font-size: 16px;
    }

    .team-details__address-box {
      margin-left: 0;
      padding: 39px 10px 43px;
      flex-direction: column;
      align-items: baseline;
    }

    .team-details__client-info {
      gap: 10px;
      flex-direction: column;
      align-items: baseline;
    }

    .team-details__social {
      margin-top: 20px;
    }

    .coming-soon-page {
      height: auto !important;
      padding: 110px 0 120px;
    }

    .coming-soon-page__content .big-title {
      font-size: 35px;
      line-height: 45px;
    }

    .coming-soon-page .timer-box .countdown-timer li {
      float: none;
    }

    .coming-soon-page .timer-box .countdown-timer li + li {
      margin-top: 20px;
    }

    .coming-soon-page__content {
      top: 0%;
      transform: translateY(0%);
    }

    .coming-soon-page__content .inner .text p br {
      display: none;
    }

    .coming-soon-page__subscribe-box form {
      flex-direction: column;
    }

    .about-one__content {
      padding: 0px 0px 0px;
      padding-right: 0;
    }

    .blog-two-single .content-box {
      padding: 50px 30px 50px;
    }

    .blog-two-single .content-box .meta-box {
      display: block;
    }

    .blog-two-single .content-box .meta-box .meta-info {
      display: block;
      margin-left: 0px;
      margin-top: 20px;
    }

    .blog-two-single .content-box .title-box h3 {
      font-size: 20px;
      line-height: 30px;
    }

    .blog-two-single .content-box .meta-box .meta-info li + li {
      margin-left: 0px;
      margin-top: 10px;
    }
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1470px) {
  .project-two__content {
    padding: 12px 20px 12px;
    padding-right: 30px;
  }

  .project-two__title {
    font-size: 16px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1360px) {
  .services-two__top {
    padding: 63px 60px 63px;
    padding-right: 60px;
  }

  .services-two__single {
    padding: 45px 60px 45px;
    padding-right: 60px;
  }

  .services-two__single-right {
    gap: 145px;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1300px) {
}

@media only screen and (min-width: 1400px) and (max-width: 1610px) {
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
}

@media only screen and (min-width: 1320px) and (max-width: 1350px) {
}

/*--------------------------------------------------------------
# Slider All Responsice Css
--------------------------------------------------------------*/
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .main-slider-two .owl-nav {
    right: 50px;
  }

  .main-slider-three__left {
    margin-top: 20px;
  }

  .main-slider-three__title-two {
    font-size: 30px;
    line-height: 40px;
  }

  .main-slider-three__nav {
    margin-top: 20px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .main-slider-two__title {
    font-size: 60px;
    line-height: 80px;
  }

  .main-slider-two__text br {
    display: none;
  }

  .main-slider-two__text {
    font-size: 16px;
    max-width: 748px;
    width: 100%;
  }

  .main-slider-two .owl-nav {
    right: 40px;
  }

  .main-slider-three__social {
    display: none;
  }

  .main-slider-three__left {
    margin-top: 10px;
    margin-left: 0;
    padding-left: 15px;
  }

  .main-slider-three__title-one {
    font-size: 40px;
    line-height: 40px;
  }

  .main-slider-three__title-two {
    font-size: 30px;
    line-height: 40px;
  }

  .main-slider-three__title-three {
    font-size: 40px;
    line-height: 40px;
  }

  .main-slider-three__nav {
    margin-top: 20px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-slider__title {
    font-size: 55px;
    line-height: 65px;
  }

  .main-slider .owl-theme .owl-nav {
    padding: 0px 30px;
  }

  .main-slider-two {
    padding-left: 0;
  }

  .main-slider-two__title {
    font-size: 60px;
    line-height: 80px;
  }

  .main-slider-two__text br {
    display: none;
  }

  .main-slider-two__text {
    font-size: 16px;
    max-width: 748px;
    width: 100%;
  }

  .main-slider-two .owl-nav {
    display: none;
  }

  .main-slider-three__social {
    display: none;
  }

  .main-slider-three__left {
    margin-top: 10px;
    margin-left: 0;
    padding-left: 15px;
  }

  .main-slider-three__title-one {
    font-size: 40px;
    line-height: 40px;
  }

  .main-slider-three__title-two {
    font-size: 30px;
    line-height: 40px;
  }

  .main-slider-three__title-three {
    font-size: 40px;
    line-height: 40px;
  }

  .main-slider-three__nav {
    margin-top: 20px;
  }
}

@media only screen and (max-width: 767px) {
  .main-slider__title {
    font-size: 25px;
    line-height: 35px;
  }

  .main-slider .owl-theme .owl-nav {
    display: none;
  }

  .main-slider-two {
    padding-left: 0;
  }

  .main-slider-two__title {
    font-size: 25px;
    line-height: 40px;
  }

  .main-slider-two__text br {
    display: none;
  }

  .main-slider-two__text {
    font-size: 16px;
  }

  .main-slider-two .item {
    padding-top: 139px;
    padding-bottom: 240px;
  }

  .main-slider-two .owl-nav {
    display: none;
  }

  .main-slider-three__social {
    display: none;
  }

  .main-slider-three__left {
    margin-top: 60px;
    margin-left: 0;
    text-align: center;
  }

  .main-slider-three__title-one {
    font-size: 40px;
    line-height: 40px;
  }

  .main-slider-three__title-two {
    font-size: 30px;
    line-height: 40px;
  }

  .main-slider-three__title-three {
    font-size: 40px;
    line-height: 40px;
  }

  .main-slider-three__nav {
    margin-top: 20px;
    margin-bottom: 20px;
    justify-content: center;
  }
}

@media only screen and (min-width: 1400px) and (max-width: 1715px) {
  .main-slider-three__left {
    margin-top: 20px;
  }

  .main-slider-three__title-two {
    font-size: 38px;
    line-height: 48px;
  }
}

@media only screen and (min-width: 1421px) and (max-width: 1500px) {
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
}

/*--------------------------------------------------------------
# Main Menu All Responsice Css
--------------------------------------------------------------*/
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .main-menu__btn-box {
    display: none;
  }

  .main-menu__search-box {
    margin-right: 0;
  }

  .main-menu-two__wrapper-inner {
    padding: 0 20px 0;
  }

  .main-menu-two__right {
    gap: 30px;
  }

  /* .main-menu-two__search-and-nav-sidebar-icon {
    display: none;
  } */
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .main-menu__top {
    display: none;
  }

  .main-menu-two__wrapper-inner {
    padding: 0 60px 0;
  }

  .main-menu-two .mobile-nav__toggler:hover {
    color: var(--tecture-white);
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .main-menu__top {
    display: none;
  }

  .main-menu-two__wrapper-inner {
    padding: 0 30px 0;
  }

  .main-menu-two .mobile-nav__toggler:hover {
    color: var(--tecture-white);
  }
}

@media (max-width: 767px) {
  .main-menu__top {
    display: none;
  }

  .main-menu__bottom {
    padding: 0 15px 0;
  }

  .main-menu__wrapper-inner {
    padding-left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--tecture-white);
  }

  .main-menu__search-and-btn-box {
    display: none;
  }

  .main-menu__logo {
    position: relative;
    padding: 14px 0px;
  }

  .main-menu-two__wrapper-inner {
    padding: 0 0 0 15px;
  }

  .main-menu-two__search-and-nav-sidebar-icon {
    display: none;
  }

  .main-menu-two__right {
    gap: 0;
  }

  .main-menu-two .mobile-nav__toggler:hover {
    color: var(--tecture-white);
  }
}

@media only screen and (min-width: 1400px) and (max-width: 1580px) {
  .main-menu-two__wrapper-inner {
    padding: 0 20px 0;
  }

  .main-menu-two__right {
    gap: 30px;
  }
}

/* The End */
